{% extends 'base.html.twig' %}

{% block title %}D<PERSON><PERSON> de la demande{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        // Wait for document to be fully loaded
        $(document).ready(function() {
            // Initialize delete button functionality
            initializeDeleteButton();

            // Initialize form submissions with AJAX
            initializeFormSubmissions();

            // Initialize modal functionality for close button
            initializeModalCloseButtons();

            // Initialize technician selection
            initializeTechnicianSelection();
        });

        // Function to initialize technician selection
        function initializeTechnicianSelection() {
            const technicienInput = $('#technicien-input');
            const technicienHidden = $('#technicien');

            if (technicienInput.length > 0) {
                technicienInput.on('input', function() {
                    const selectedName = $(this).val();
                    const selectedOption = $('#technicien-list option').filter(function() {
                        return $(this).val() === selectedName;
                    });

                    if (selectedOption.length > 0) {
                        const selectedId = selectedOption.data('id');
                        technicienHidden.val(selectedId);

                        // Add visual feedback
                        $(this).addClass('border-green-300 bg-green-50');
                        $(this).removeClass('border-red-300 bg-red-50');
                    } else {
                        technicienHidden.val('');

                        if (selectedName) {
                            $(this).addClass('border-red-300 bg-red-50');
                            $(this).removeClass('border-green-300 bg-green-50');
                        } else {
                            $(this).removeClass('border-green-300 bg-green-50 border-red-300 bg-red-50');
                        }
                    }
                });
            }
        }

        // Delete button functionality
        function initializeDeleteButton() {
            const deleteButton = $('#delete-button');
            if (deleteButton.length === 0) return;

            deleteButton.on('click', function(e) {
                e.preventDefault();

                // Use SweetAlert2 for confirmation
                Swal.fire({
                    title: 'Êtes-vous sûr ?',
                    text: "Cette action ne peut pas être annulée !",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#ef4444',
                    cancelButtonColor: '#64748b',
                    confirmButtonText: 'Oui, supprimer',
                    cancelButtonText: 'Annuler',
                    customClass: {
                        confirmButton: 'btn btn-danger',
                        cancelButton: 'btn btn-secondary'
                    }
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Submit the delete form
                        $('#delete-form').submit();
                    }
                });
            });
        }

        // Form submissions with AJAX
        function initializeFormSubmissions() {
            // Gestionnaire form
            const gestionnaireForm = $('#gestionnaire-update-form');
            if (gestionnaireForm.length > 0) {
                gestionnaireForm.on('submit', function(e) {
                    e.preventDefault();
                    submitFormWithAjax($(this), 'gestionnaire-update-status', updateGestionnaireFields);
                });
            }

            // Admin form
            const adminForm = $('#admin-update-form');
            if (adminForm.length > 0) {
                adminForm.on('submit', function(e) {
                    e.preventDefault();
                    submitFormWithAjax($(this), 'admin-update-status', updateAdminFields);
                });
            }
        }

        // Submit form with AJAX
        function submitFormWithAjax(form, statusElementId, successCallback) {
            const formData = new FormData(form[0]);

            $.ajax({
                url: form.attr('action'),
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                success: function(data) {
                    if (data.success) {
                        // Show success message
                        const statusElement = $('#' + statusElementId);
                        if (statusElement.length > 0) {
                            statusElement.removeClass('hidden');
                            setTimeout(() => {
                                statusElement.addClass('hidden');
                            }, 3000);
                        }

                        // Show toast notification
                        Toast.fire({
                            icon: 'success',
                            title: data.message || 'Mise à jour effectuée'
                        });

                        // Update fields
                        if (successCallback) {
                            successCallback(data);
                        }
                    } else {
                        // Show error message
                        Toast.fire({
                            icon: 'error',
                            title: data.message || 'Une erreur est survenue'
                        });
                    }
                },
                error: function() {
                    Toast.fire({
                        icon: 'error',
                        title: 'Une erreur est survenue lors de la communication avec le serveur'
                    });
                }
            });
        }

        // Update gestionnaire fields
        function updateGestionnaireFields(data) {
            if (data.wbs) {
                $('#wbs-value').text(data.wbs);
            }
            if (data.numeroOF) {
                // Find the numeroOF display element and update it
                $('.mt-1.text-sm.text-gray-900.sm\\:mt-0.sm\\:col-span-2').each(function() {
                    if ($(this).prev().text() === 'Numéro OF') {
                        $(this).text(data.numeroOF);
                    }
                });
            }
            if (data.dateGestionnaire) {
                $('#date-gestionnaire-value').text(data.dateGestionnaire);
            }
            if (data.commentaire) {
                $('#commentaire-gestionnaire').html(data.commentaire.replace(/\n/g, '<br>'));
            }
        }

        // Update admin fields
        function updateAdminFields(data) {
            if (data.statusChanged) {
                // Update status badge
                const statusBadge = $('#status-badge');
                statusBadge.removeClass();
                statusBadge.addClass('px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full');

                switch (data.newStatus) {
                    case 'en_attente':
                        statusBadge.addClass('bg-yellow-100 text-yellow-800');
                        break;
                    case 'validee':
                        statusBadge.addClass('bg-blue-100 text-blue-800');
                        break;
                    case 'refusee':
                        statusBadge.addClass('bg-red-100 text-red-800');
                        break;
                    case 'traitee':
                        statusBadge.addClass('bg-green-100 text-green-800');
                        break;
                    case 'cloturee':
                        statusBadge.addClass('bg-purple-100 text-purple-800');
                        break;
                    default:
                        statusBadge.addClass('bg-gray-100 text-gray-800');
                }

                statusBadge.text(data.statut);

                // Reload the page to update the timeline and action buttons
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                // Update fields without reloading
                updateGestionnaireFields(data);
            }
        }

        // Initialize modal close buttons
        function initializeModalCloseButtons() {
            $('.modal-close-btn').on('click', function() {
                const modalId = $(this).data('modal-id');
                closeModal(modalId);
            });

            // Close modal when clicking on overlay
            $('.modal-overlay').on('click', function(e) {
                if (e.target === this) {
                    const modalId = $(this).closest('.modal-container').attr('id');
                    closeModal(modalId);
                }
            });

            // Close modal with Escape key
            $(document).on('keydown', function(e) {
                if (e.key === 'Escape') {
                    $('.modal-container:not(.hidden)').each(function() {
                        closeModal($(this).attr('id'));
                    });
                }
            });
        }

        // Open validate modal
        function openValidateModal(id) {
            const modal = $('#validate-modal');

            // Reset form
            $('#technicien-input').val('');
            $('#technicien').val('');
            $('#validate-commentaire').val('');

            // Show modal
            modal.removeClass('hidden');

            // Focus on input after animation
            setTimeout(() => {
                $('#technicien-input').focus();
            }, 300);
        }

        // Open reject modal
        function openRejectModal(id) {
            const modal = $('#reject-modal');

            // Reset form
            $('#reject-commentaire').val('');

            // Show modal
            modal.removeClass('hidden');

            // Focus on input after animation
            setTimeout(() => {
                $('#reject-commentaire').focus();
            }, 300);
        }

        // Open process modal
        function openProcessModal(id) {
            const modal = $('#process-modal');

            // Reset form
            $('#process-commentaire').val('');

            // Show modal
            modal.removeClass('hidden');

            // Focus on input after animation
            setTimeout(() => {
                $('#process-commentaire').focus();
            }, 300);
        }

        // Open close modal
        function openCloseModal(id) {
            const modal = $('#close-modal');

            // Reset form
            $('#close-commentaire').val('');

            // Show modal
            modal.removeClass('hidden');

            // Focus on input after animation
            setTimeout(() => {
                $('#close-commentaire').focus();
            }, 300);
        }

        // Close modal
        function closeModal(id) {
            const modal = $('#' + id);

            // Add animation class
            modal.find('.modal-content').addClass('animate__fadeOutUp');

            // Hide modal after animation
            setTimeout(() => {
                modal.addClass('hidden');
                modal.find('.modal-content').removeClass('animate__fadeOutUp');
            }, 200);
        }


    </script>
{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" />
    <style>
        /* Animation pour la ligne de progression vers la prochaine étape */
        @keyframes nextStepProgress {
            0% { left: -30%; }
            100% { left: 130%; }
        }

        @keyframes pulseGlow {
            0% { box-shadow: 0 0 0 0 rgba(74, 222, 128, 0.4); }
            70% { box-shadow: 0 0 0 5px rgba(74, 222, 128, 0); }
            100% { box-shadow: 0 0 0 0 rgba(74, 222, 128, 0); }
        }

        @keyframes colorPulse {
            0% { background-color: #DCFCE7; } /* green-100 */
            50% { background-color: #BBF7D0; } /* green-200 */
            100% { background-color: #DCFCE7; } /* green-100 */
        }

        .next-step-line {
            position: relative;
            overflow: hidden;
            background-color: #DCFCE7 !important; /* green-100, très discret */
            height: 3px !important; /* Ligne moins épaisse */
            border-radius: 1.5px !important; /* Bords arrondis */
            animation: colorPulse 4s infinite;
            z-index: 1;
            margin-top: 1px !important;
            margin-bottom: 1px !important;
        }

        .next-step-line::after {
            content: '';
            position: absolute;
            top: 0;
            left: -30%;
            height: 100%;
            width: 30%;
            background: linear-gradient(to right,
                rgba(74, 222, 128, 0) 0%,
                rgba(74, 222, 128, 0.6) 50%,
                rgba(74, 222, 128, 0) 100%);
            animation: nextStepProgress 2s ease-in-out infinite;
            z-index: 2;
        }

        /* Styles pour les modales */
        .modal-backdrop {
            backdrop-filter: blur(2px);
        }

        /* Amélioration des champs de formulaire */
        textarea:focus, input:focus {
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
        }

        /* Animation pour les boutons */
        .btn-hover-effect {
            transition: all 0.2s ease-in-out;
        }

        .btn-hover-effect:hover {
            transform: translateY(-2px);
        }

        /* Styles pour les textarea */
        textarea {
            min-height: 100px;
            resize: vertical;
        }

        /* Amélioration de la visibilité des champs obligatoires */
        .required-field {
            position: relative;
        }

        .required-field::after {
            content: '*';
            color: #ef4444;
            position: absolute;
            top: 0;
            right: -10px;
        }
    </style>
{% endblock %}

{% block body %}
<div class="max-w-7xl mx-auto space-y-6 py-6">
    {# En-tête avec boutons d'action #}
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900">Détail de la demande #{{ demande.id }}</h1>
        <div class="flex space-x-3">
            <a href="{{ path('app_demande_index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all duration-200 ease-in-out">
                <i class="fas fa-arrow-left mr-2"></i>
                Retour à la liste
            </a>
            {% if demande.statut not in ['traitee', 'refusee'] %}
                <a href="{{ path('app_demande_edit', {'id': demande.id}) }}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all duration-200 ease-in-out">
                    <i class="fas fa-edit mr-2"></i>
                    Modifier
                </a>
            {% endif %}
        </div>
    </div>

    {# Formulaire de suppression caché #}
    {% if demande.statut == 'en_attente' %}
        <form id="delete-form" method="post" action="{{ path('app_demande_delete', {'id': demande.id}) }}" class="hidden">
            <input type="hidden" name="_token" value="{{ csrf_token('delete' ~ demande.id) }}">
        </form>
    {% endif %}

    {# Timeline d'avancement #}
    <div class="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
        <div class="px-4 py-5 sm:px-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900">Avancement de la demande</h3>
            <p class="mt-1 max-w-2xl text-sm text-gray-500">Suivi des étapes de traitement</p>
        </div>
        <div class="border-t border-gray-200 px-4 py-5 sm:p-6">
            <div class="flex items-center justify-between w-full">
                {# Étape 1: Création #}
                <div class="flex flex-col items-center">
                    <div class="w-8 h-8 rounded-full bg-green-500 flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <span class="mt-2 text-sm font-medium text-gray-900">Création</span>
                    <span class="text-xs text-gray-500">{{ demande.dateCreation|date('d/m/Y') }}</span>
                </div>

                {# Ligne de connexion #}
                <div class="flex-1 h-1 mx-2 {% if demande.statut == 'refusee' %}bg-red-500{% elseif demande.statut != 'en_attente' %}bg-green-500{% else %}next-step-line{% endif %}"></div>

                {# Étape 2: Validation #}
                <div class="flex flex-col items-center">
                    <div class="w-8 h-8 rounded-full {% if demande.statut == 'en_attente' %}bg-gray-300{% elseif demande.statut == 'refusee' %}bg-red-500{% else %}bg-green-500{% endif %} flex items-center justify-center">
                        {% if demande.statut == 'en_attente' %}
                            <span class="text-white font-bold">2</span>
                        {% elseif demande.statut == 'refusee' %}
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                            </svg>
                        {% else %}
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                            </svg>
                        {% endif %}
                    </div>
                    <span class="mt-2 text-sm font-medium text-gray-900">Validation</span>
                    <span class="text-xs text-gray-500">
                        {% if demande.statut == 'en_attente' %}
                            En attente
                        {% elseif demande.statut == 'refusee' %}
                            Refusée
                        {% else %}
                            Validée
                        {% endif %}
                    </span>
                </div>

                {# Ligne de connexion #}
                <div class="flex-1 h-1 mx-2 {% if demande.statut == 'traitee' or demande.statut == 'cloturee' %}bg-green-500{% elseif demande.statut == 'refusee' %}bg-red-300{% elseif demande.statut == 'validee' %}next-step-line{% else %}bg-gray-300{% endif %}"></div>

                {# Étape 3: Traitement #}
                <div class="flex flex-col items-center">
                    <div class="w-8 h-8 rounded-full {% if demande.statut == 'traitee' or demande.statut == 'cloturee' %}bg-green-500{% else %}bg-gray-300{% endif %} flex items-center justify-center">
                        {% if demande.statut == 'traitee' or demande.statut == 'cloturee' %}
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                            </svg>
                        {% else %}
                            <span class="text-white font-bold">3</span>
                        {% endif %}
                    </div>
                    <span class="mt-2 text-sm font-medium text-gray-900">Traitement</span>
                    <span class="text-xs text-gray-500">
                        {% if demande.statut == 'traitee' or demande.statut == 'cloturee' %}
                            Terminé
                        {% elseif demande.statut == 'refusee' %}
                            Annulé
                        {% else %}
                            À venir
                        {% endif %}
                    </span>
                </div>

                {# Ligne de connexion #}
                <div class="flex-1 h-1 mx-2 {% if demande.statut == 'cloturee' %}bg-green-500{% elseif demande.statut == 'traitee' %}next-step-line{% else %}bg-gray-300{% endif %}"></div>

                {# Étape 4: Clôture #}
                <div class="flex flex-col items-center">
                    <div class="w-8 h-8 rounded-full {% if demande.statut == 'cloturee' %}bg-green-500{% else %}bg-gray-300{% endif %} flex items-center justify-center">
                        {% if demande.statut == 'cloturee' %}
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                            </svg>
                        {% else %}
                            <span class="text-white font-bold">4</span>
                        {% endif %}
                    </div>
                    <span class="mt-2 text-sm font-medium text-gray-900">Clôture</span>
                    <span class="text-xs text-gray-500">
                        {% if demande.statut == 'cloturee' %}
                            Terminé
                        {% elseif demande.statut == 'refusee' %}
                            Annulé
                        {% elseif demande.statut == 'traitee' %}
                            En attente
                        {% else %}
                            À venir
                        {% endif %}
                    </span>
                </div>
            </div>
        </div>
    </div>

    {# Boutons d'action contextuels #}
    <div class="flex justify-end space-x-3 mb-6">
        {% if demande.statut == 'en_attente' and app.user and app.user.isAdmin %}
            <button type="button" onclick="openValidateModal({{ demande.id }})" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 ease-in-out">
                <i class="fas fa-check mr-2"></i>
                Valider
            </button>
            <button type="button" onclick="openRejectModal({{ demande.id }})" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-all duration-200 ease-in-out">
                <i class="fas fa-times mr-2"></i>
                Refuser
            </button>
        {% endif %}

        {% if demande.statut == 'validee' and app.user and demande.technicien and demande.technicien.id == app.user.id %}
            <button type="button" onclick="openProcessModal({{ demande.id }})" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200 ease-in-out">
                <i class="fas fa-check-circle mr-2"></i>
                Marquer comme traitée
            </button>
        {% endif %}

        {% if demande.statut == 'traitee' and app.user and demande.technicien and demande.technicien.id == app.user.id %}
            <button type="button" onclick="openCloseModal({{ demande.id }})" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-200 ease-in-out">
                <i class="fas fa-archive mr-2"></i>
                Clôturer la demande
            </button>
        {% endif %}

        {% if demande.statut == 'en_attente' and (app.user.id == demande.demandeur.id or app.user.isAdmin) %}
            <button type="button" id="delete-button" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-all duration-200 ease-in-out">
                <i class="fas fa-trash-alt mr-2"></i>
                Supprimer
            </button>
        {% endif %}
    </div>
    {# Informations de la demande #}
    <div class="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
        <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
            <div>
                <h3 class="text-lg leading-6 font-medium text-gray-900">Informations de la demande</h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500">Créée le {{ demande.dateCreation|date('d/m/Y') }}</p>
            </div>
            <span id="status-badge" class="px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full
                {% if demande.statut == 'en_attente' %}
                    bg-yellow-100 text-yellow-800
                {% elseif demande.statut == 'validee' %}
                    bg-blue-100 text-blue-800
                {% elseif demande.statut == 'refusee' %}
                    bg-red-100 text-red-800
                {% elseif demande.statut == 'traitee' %}
                    bg-green-100 text-green-800
                {% elseif demande.statut == 'cloturee' %}
                    bg-purple-100 text-purple-800
                {% else %}
                    bg-gray-100 text-gray-800
                {% endif %}
            ">
                {{ demande.statutLabel }}
            </span>
        </div>
        <div class="border-t border-gray-200">
            <dl>
                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Demandeur</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ demande.demandeur.nomComplet }}</dd>
                </div>
                <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Date souhaitée de réception</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ demande.dateSouhaitee|date('d/m/Y') }}</dd>
                </div>
                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Gestionnaire assigné</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                        {% if demande.technicien %}
                            {{ demande.technicien.nomComplet }}
                        {% else %}
                            <span class="text-gray-400">Non assigné</span>
                        {% endif %}
                    </dd>
                </div>
                <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Urgence</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                            {% if demande.urgence == 'haute' %}
                                bg-red-100 text-red-800
                            {% elseif demande.urgence == 'moyenne' %}
                                bg-yellow-100 text-yellow-800
                            {% else %}
                                bg-green-100 text-green-800
                            {% endif %}
                        ">
                            {{ demande.urgenceLabel }}
                        </span>
                    </dd>
                </div>
                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Nature</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ demande.natureLabel }}</dd>
                </div>
                <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Destinataire</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                        {% if demande.destinataireUser %}
                            <span class="me-3">{{ demande.destinataireUser.nomComplet }}</span>
                        {% endif %}
                        {% if demande.destinataire %}
                            <span class="text-gray-600">{{ demande.destinataire }}</span>
                        {% else %}
                            <span class="text-gray-400">Non défini</span>
                        {% endif %}
                    </dd>
                </div>
                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Centre de coût</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                        {% if demande.compte %}
                            {{ demande.compte }}
                        {% else %}
                            <span class="text-gray-400">Non défini</span>
                        {% endif %}
                    </dd>
                </div>

                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Potentiel</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                        {% if demande.potentielLabel %}
                            {{ demande.potentielLabel }}
                        {% else %}
                            <span class="text-gray-400">Non défini</span>
                        {% endif %}
                    </dd>
                </div>

                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Nom du client</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                        {% if demande.nomClient %}
                            {{ demande.nomClient }}
                        {% else %}
                            <span class="text-gray-400">Non défini</span>
                        {% endif %}
                    </dd>
                </div>
                <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">WBS</dt>
                    <dd id="wbs-value" class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                        {% if demande.wbs %}
                            {{ demande.wbs }}
                        {% else %}
                            <span class="text-gray-400">Non défini</span>
                        {% endif %}
                    </dd>
                </div>
                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Numéro OF</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                        {% if demande.numeroOF %}
                            {{ demande.numeroOF }}
                        {% else %}
                            <span class="text-gray-400">Non défini</span>
                        {% endif %}
                    </dd>
                </div>
                <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Date gestionnaire</dt>
                    <dd id="date-gestionnaire-value" class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                        {% if demande.dateGestionnaire %}
                            {{ demande.dateGestionnaire|date('d/m/Y') }}
                        {% else %}
                            <span class="text-gray-400">Non définie</span>
                        {% endif %}
                    </dd>
                </div>
                <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Commentaire du demandeur</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ demande.commentaireDemandeur|default('Aucun commentaire')|nl2br|raw }}</dd>
                </div>
                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Commentaire du gestionnaire</dt>
                    <dd id="commentaire-gestionnaire" class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ demande.commentaireGestionnaire|default('Aucun commentaire')|nl2br|raw }}</dd>
                </div>
            </dl>
        </div>
    </div>
    <div class="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
        <div class="px-4 py-5 sm:px-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900">Items demandés</h3>
            <p class="mt-1 max-w-2xl text-sm text-gray-500">Liste détaillée des références et informations</p>
        </div>
        <div class="border-t border-gray-200">
            {% for item in demande.items %}
                <div class="{% if loop.index is odd %}bg-gray-50{% else %}bg-white{% endif %} px-4 py-5 sm:px-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div>
                            <dt class="text-sm font-medium text-gray-500 mb-1">Référence</dt>
                            <dd class="text-sm text-gray-900 font-medium">{{ item.reference }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500 mb-1">Quantité</dt>
                            <dd class="text-sm text-gray-900">{{ item.quantite }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500 mb-1">Date souhaitée</dt>
                            <dd class="text-sm text-gray-900">
                                {% if item.dateSouhaitee %}
                                    {{ item.dateSouhaitee|date('d/m/Y') }}
                                {% else %}
                                    <span class="text-gray-400">Non définie</span>
                                {% endif %}
                            </dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500 mb-1">Lien plan</dt>
                            <dd class="text-sm text-gray-900">
                                {% if item.lienPlan %}
                                    <a href="{{ item.lienPlan }}" target="_blank" class="text-blue-600 hover:text-blue-800 hover:underline inline-flex items-center">
                                        <i class="fas fa-external-link-alt mr-1"></i> Voir le plan
                                    </a>
                                {% else %}
                                    <span class="text-gray-400">Non défini</span>
                                {% endif %}
                            </dd>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    </div>
    {# Formulaire pour les gestionnaires assignés #}
    {% if demande.technicien and app.user and demande.technicien.id == app.user.id and demande.statut == 'validee' %}
        <div class="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
            <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
                <div>
                    <h3 class="text-lg leading-6 font-medium text-gray-900">Gestion de la demande (Gestionnaire)</h3>
                    <p class="mt-1 max-w-2xl text-sm text-gray-500">Mettre à jour les informations de la demande</p>
                </div>
                <div id="gestionnaire-update-status" class="hidden">
                    <span class="px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                        <i class="fas fa-check mr-1"></i> Mise à jour effectuée
                    </span>
                </div>
            </div>
            <div class="border-t border-gray-200 px-4 py-5 sm:p-6">
                <form id="gestionnaire-update-form" method="post" action="{{ path('app_demande_update_status', {'id': demande.id}) }}">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="gestionnaire-wbs" class="block text-sm font-medium text-gray-700 mb-1">WBS</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-hashtag text-gray-400"></i>
                                </div>
                                <input type="text" id="gestionnaire-wbs" name="wbs" value="{{ demande.wbs }}" class="pl-10 block w-full py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md">
                            </div>
                        </div>
                        <div>
                            <label for="gestionnaire-dateGestionnaire" class="block text-sm font-medium text-gray-700 mb-1">Date gestionnaire</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-calendar-alt text-gray-400"></i>
                                </div>
                                <input type="date" id="gestionnaire-dateGestionnaire" name="dateGestionnaire" value="{{ demande.dateGestionnaire ? demande.dateGestionnaire|date('Y-m-d') : '' }}" class="pl-10 block w-full py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md">
                            </div>
                        </div>
                        <div>
                            <label for="gestionnaire-numeroOF" class="block text-sm font-medium text-gray-700 mb-1">Numéro OF</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-hashtag text-gray-400"></i>
                                </div>
                                <input type="text" id="gestionnaire-numeroOF" name="numeroOF" value="{{ demande.numeroOF }}" class="pl-10 block w-full py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md" placeholder="Ex: OF123456">
                            </div>
                        </div>
                    </div>

                    <div class="mt-6">
                        <label for="gestionnaire-commentaire" class="block text-sm font-medium text-gray-700 mb-1">Commentaire</label>
                        <div class="relative">
                            <div class="absolute top-3 left-3 text-gray-400">
                                <i class="fas fa-comment"></i>
                            </div>
                            <textarea id="gestionnaire-commentaire" name="commentaire" rows="5" class="pl-10 shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md" placeholder="Commentaire concernant cette demande">{{ demande.commentaireGestionnaire }}</textarea>
                            <input type="hidden" name="update_type" value="gestionnaire">
                            <input type="hidden" name="edit_full_comment" value="true">
                        </div>
                    </div>
                    <div class="mt-4 flex justify-end">
                        <button type="submit" id="gestionnaire-submit-btn" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                            <i class="fas fa-save mr-2"></i> Mettre à jour
                        </button>
                    </div>
                    <input type="hidden" name="_token" value="{{ csrf_token('update_gestionnaire_' ~ demande.id) }}">
                </form>
            </div>
        </div>
    {% endif %}

    {# Formulaire pour les administrateurs #}
    {% if is_granted('ROLE_ADMIN') %}
        <div class="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
            <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
                <div>
                    <h3 class="text-lg leading-6 font-medium text-gray-900">Gestion de la demande (Admin)</h3>
                    <p class="mt-1 max-w-2xl text-sm text-gray-500">Mettre à jour le statut et ajouter un commentaire</p>
                </div>
                <div id="admin-update-status" class="hidden">
                    <span class="px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                        <i class="fas fa-check mr-1"></i> Mise à jour effectuée
                    </span>
                </div>
            </div>
            <div class="border-t border-gray-200 px-4 py-5 sm:p-6">
                <form id="admin-update-form" action="{{ path('app_demande_update_status', {'id': demande.id}) }}" method="post">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="statut" class="block text-sm font-medium text-gray-700 mb-1">Statut</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-tasks text-gray-400"></i>
                                </div>
                                <select id="statut" name="statut" class="pl-10 block w-full py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md">
                                    <option value="en_attente" {% if demande.statut == 'en_attente' %}selected{% endif %}>En attente</option>
                                    <option value="validee" {% if demande.statut == 'validee' %}selected{% endif %}>Validée</option>
                                    <option value="refusee" {% if demande.statut == 'refusee' %}selected{% endif %}>Refusée</option>
                                    <option value="traitee" {% if demande.statut == 'traitee' %}selected{% endif %}>Traitée</option>
                                    <option value="cloturee" {% if demande.statut == 'cloturee' %}selected{% endif %}>Clôturée</option>
                                </select>
                            </div>
                        </div>
                        <div>
                            <label for="wbs" class="block text-sm font-medium text-gray-700 mb-1">WBS</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-hashtag text-gray-400"></i>
                                </div>
                                <input type="text" id="wbs" name="wbs" value="{{ demande.wbs }}" class="pl-10 block w-full py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md">
                            </div>
                        </div>
                        <div>
                            <label for="dateGestionnaire" class="block text-sm font-medium text-gray-700 mb-1">Date gestionnaire</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-calendar-alt text-gray-400"></i>
                                </div>
                                <input type="date" id="dateGestionnaire" name="dateGestionnaire" value="{{ demande.dateGestionnaire ? demande.dateGestionnaire|date('Y-m-d') : '' }}" class="pl-10 block w-full py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md">
                            </div>
                        </div>
                        <div>
                            <label for="numeroOF" class="block text-sm font-medium text-gray-700 mb-1">Numéro OF</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-hashtag text-gray-400"></i>
                                </div>
                                <input type="text" id="numeroOF" name="numeroOF" value="{{ demande.numeroOF }}" class="pl-10 block w-full py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md" placeholder="Ex: OF123456">
                            </div>
                        </div>
                    </div>

                    <div class="mt-6">
                        <label for="commentaire" class="block text-sm font-medium text-gray-700 mb-1">Commentaire</label>
                        <div class="relative">
                            <div class="absolute top-3 left-3 text-gray-400">
                                <i class="fas fa-comment"></i>
                            </div>
                            <textarea id="commentaire" name="commentaire" rows="5" class="pl-10 shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md" placeholder="Commentaire du gestionnaire">{{ demande.commentaireGestionnaire }}</textarea>
                            <input type="hidden" name="update_type" value="admin">
                            <input type="hidden" name="edit_full_comment" value="true">
                        </div>
                    </div>
                    <div class="mt-4 flex justify-end">
                        <button type="submit" id="admin-submit-btn" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                            <i class="fas fa-save mr-2"></i> Mettre à jour
                        </button>
                    </div>
                    <input type="hidden" name="_token" value="{{ csrf_token('update_admin_' ~ demande.id) }}">
                </form>
            </div>
        </div>
    {% endif %}

</div>

<!-- Modal de validation -->
<div id="validate-modal" class="modal-container fixed z-10 inset-0 overflow-y-auto hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <div class="modal-overlay fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="modal-content inline-block align-middle bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:max-w-lg sm:w-full animate__animated animate__fadeInDown animate__faster">
            <form id="validate-form" method="post" action="{{ path('app_validation_box_validate', {'id': demande.id}) }}">
                <input type="hidden" name="_token" value="{{ csrf_token('validate_demande') }}">
                <div class="bg-white px-6 pt-6 pb-6">
                    <div class="flex items-start mb-6">
                        <div class="flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 mr-4">
                            <svg class="h-6 w-6 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-xl leading-6 font-medium text-gray-900 mb-2" id="modal-title">
                                Valider la demande
                            </h3>
                            <p class="text-sm text-gray-500 mb-4">
                                Êtes-vous sûr de vouloir valider cette demande ? Veuillez assigner un gestionnaire et ajouter un commentaire si nécessaire.
                            </p>
                        </div>
                    </div>

                    <div class="space-y-6">
                        {% if demande.gestionnairePreferentiel %}
                            <div class="bg-green-50 p-4 rounded-md border border-green-100">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-user-check text-green-600"></i>
                                    </div>
                                    <div class="ml-3">
                                        <h4 class="text-sm font-medium text-green-800">Gestionnaire préférentiel</h4>
                                        <p class="text-sm text-green-700">{{ demande.gestionnairePreferentiel.nomComplet }}</p>
                                    </div>
                                </div>
                            </div>
                        {% endif %}

                        <div class="bg-blue-50 p-4 rounded-md border border-blue-100">
                            <label for="technicien-input" class="block text-sm font-medium text-blue-800 mb-2">Assigner à un gestionnaire <span class="text-red-500">*</span></label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-user text-gray-400"></i>
                                </div>
                                <input type="text" id="technicien-input" list="technicien-list" placeholder="Rechercher un gestionnaire..."
                                    class="pl-10 block w-full py-3 px-4 border border-blue-200 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary text-base" required>
                            </div>
                            <datalist id="technicien-list">
                                {% for technicien in techniciens %}
                                    <option value="{{ technicien.nomComplet }}" data-id="{{ technicien.id }}">
                                {% endfor %}
                            </datalist>
                            <input type="hidden" id="technicien" name="technicien" value="">
                        </div>

                        <div>
                            <label for="validate-commentaire" class="block text-sm font-medium text-gray-700 mb-2">Commentaire (optionnel)</label>
                            <div class="relative">
                                <div class="absolute top-3 left-3 text-gray-400">
                                    <i class="fas fa-comment"></i>
                                </div>
                                <textarea id="validate-commentaire" name="commentaire" rows="4"
                                    class="pl-10 block w-full py-3 px-4 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary text-base"
                                    placeholder="Ajoutez un commentaire concernant cette validation..."></textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-gray-50 px-6 py-4 flex flex-col sm:flex-row-reverse sm:justify-end gap-3">
                    <button type="submit" class="w-full inline-flex justify-center items-center rounded-md border border-transparent shadow-sm px-4 py-3 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:w-auto transition-colors duration-200 btn-hover-effect">
                        <i class="fas fa-check mr-2"></i> Valider
                    </button>
                    <button type="button" class="modal-close-btn w-full inline-flex justify-center items-center rounded-md border border-gray-300 shadow-sm px-4 py-3 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:w-auto transition-colors duration-200 btn-hover-effect" data-modal-id="validate-modal">
                        <i class="fas fa-times mr-2"></i> Annuler
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal de refus -->
<div id="reject-modal" class="modal-container fixed z-10 inset-0 overflow-y-auto hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <div class="modal-overlay fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="modal-content inline-block align-middle bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:max-w-lg sm:w-full animate__animated animate__fadeInDown animate__faster">
            <form id="reject-form" method="post" action="{{ path('app_validation_box_reject', {'id': demande.id}) }}">
                <input type="hidden" name="_token" value="{{ csrf_token('reject_demande') }}">
                <div class="bg-white px-6 pt-6 pb-6">
                    <div class="flex items-start mb-6">
                        <div class="flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mr-4">
                            <svg class="h-6 w-6 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-xl leading-6 font-medium text-gray-900 mb-2" id="modal-title">
                                Refuser la demande
                            </h3>
                            <p class="text-sm text-gray-500 mb-4">
                                Êtes-vous sûr de vouloir refuser cette demande ? Veuillez indiquer la raison du refus ci-dessous.
                            </p>
                        </div>
                    </div>

                    <div class="space-y-6">
                        <div class="bg-red-50 p-4 rounded-md border border-red-100">
                            <label for="reject-commentaire" class="block text-sm font-medium text-red-800 mb-2">Raison du refus <span class="text-red-500">*</span></label>
                            <div class="relative">
                                <div class="absolute top-3 left-3 text-gray-400">
                                    <i class="fas fa-comment-slash"></i>
                                </div>
                                <textarea id="reject-commentaire" name="commentaire" rows="5"
                                    class="pl-10 block w-full py-3 px-4 border border-red-200 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 text-base"
                                    placeholder="Veuillez expliquer pourquoi cette demande est refusée..." required></textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-gray-50 px-6 py-4 flex flex-col sm:flex-row-reverse sm:justify-end gap-3">
                    <button type="submit" class="w-full inline-flex justify-center items-center rounded-md border border-transparent shadow-sm px-4 py-3 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:w-auto transition-colors duration-200 btn-hover-effect">
                        <i class="fas fa-times mr-2"></i> Refuser
                    </button>
                    <button type="button" class="modal-close-btn w-full inline-flex justify-center items-center rounded-md border border-gray-300 shadow-sm px-4 py-3 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:w-auto transition-colors duration-200 btn-hover-effect" data-modal-id="reject-modal">
                        <i class="fas fa-arrow-left mr-2"></i> Annuler
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal de traitement -->
<div id="process-modal" class="modal-container fixed z-10 inset-0 overflow-y-auto hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <div class="modal-overlay fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="modal-content inline-block align-middle bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:max-w-lg sm:w-full animate__animated animate__fadeInDown animate__faster">
            <form id="process-form" method="post" action="{{ path('app_demande_update_status', {'id': demande.id}) }}">
                <input type="hidden" name="_token" value="{{ csrf_token('process_demande') }}">
                <input type="hidden" name="statut" value="traitee">
                <input type="hidden" name="update_type" value="process">
                <div class="bg-white px-6 pt-6 pb-6">
                    <div class="flex items-start mb-6">
                        <div class="flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mr-4">
                            <svg class="h-6 w-6 text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-xl leading-6 font-medium text-gray-900 mb-2" id="modal-title">
                                Marquer comme traitée
                            </h3>
                            <p class="text-sm text-gray-500 mb-4">
                                Confirmez-vous que cette demande a été traitée ? Vous pouvez ajouter un commentaire si nécessaire.
                            </p>
                        </div>
                    </div>

                    <div class="space-y-6">
                        <div>
                            <label for="process-commentaire" class="block text-sm font-medium text-gray-700 mb-2">Commentaire (optionnel)</label>
                            <div class="relative">
                                <div class="absolute top-3 left-3 text-gray-400">
                                    <i class="fas fa-comment"></i>
                                </div>
                                <textarea id="process-commentaire" name="commentaire" rows="4"
                                    class="pl-10 block w-full py-3 px-4 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary text-base"
                                    placeholder="Ajoutez un commentaire concernant le traitement de cette demande..."></textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-gray-50 px-6 py-4 flex flex-col sm:flex-row-reverse sm:justify-end gap-3">
                    <button type="submit" class="w-full inline-flex justify-center items-center rounded-md border border-transparent shadow-sm px-4 py-3 bg-green-600 text-base font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 sm:w-auto transition-colors duration-200 btn-hover-effect">
                        <i class="fas fa-check-circle mr-2"></i> Confirmer
                    </button>
                    <button type="button" class="modal-close-btn w-full inline-flex justify-center items-center rounded-md border border-gray-300 shadow-sm px-4 py-3 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:w-auto transition-colors duration-200 btn-hover-effect" data-modal-id="process-modal">
                        <i class="fas fa-times mr-2"></i> Annuler
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal de clôture -->
<div id="close-modal" class="modal-container fixed z-10 inset-0 overflow-y-auto hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <div class="modal-overlay fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="modal-content inline-block align-middle bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:max-w-lg sm:w-full animate__animated animate__fadeInDown animate__faster">
            <form id="close-form" method="post" action="{{ path('app_demande_update_status', {'id': demande.id}) }}">
                <input type="hidden" name="_token" value="{{ csrf_token('process_demande') }}">
                <input type="hidden" name="statut" value="cloturee">
                <input type="hidden" name="update_type" value="close">
                <div class="bg-white px-6 pt-6 pb-6">
                    <div class="flex items-start mb-6">
                        <div class="flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-purple-100 mr-4">
                            <svg class="h-6 w-6 text-purple-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-xl leading-6 font-medium text-gray-900 mb-2" id="modal-title">
                                Clôturer la demande
                            </h3>
                            <p class="text-sm text-gray-500 mb-4">
                                Confirmez-vous que cette demande peut être clôturée ? Vous pouvez ajouter un commentaire si nécessaire.
                            </p>
                        </div>
                    </div>

                    <div class="space-y-6">
                        <div>
                            <label for="close-commentaire" class="block text-sm font-medium text-gray-700 mb-2">Commentaire (optionnel)</label>
                            <div class="relative">
                                <div class="absolute top-3 left-3 text-gray-400">
                                    <i class="fas fa-comment"></i>
                                </div>
                                <textarea id="close-commentaire" name="commentaire" rows="4"
                                    class="pl-10 block w-full py-3 px-4 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary text-base"
                                    placeholder="Ajoutez un commentaire concernant la clôture de cette demande..."></textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-gray-50 px-6 py-4 flex flex-col sm:flex-row-reverse sm:justify-end gap-3">
                    <button type="submit" class="w-full inline-flex justify-center items-center rounded-md border border-transparent shadow-sm px-4 py-3 bg-purple-600 text-base font-medium text-white hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 sm:w-auto transition-colors duration-200 btn-hover-effect">
                        <i class="fas fa-archive mr-2"></i> Clôturer
                    </button>
                    <button type="button" class="modal-close-btn w-full inline-flex justify-center items-center rounded-md border border-gray-300 shadow-sm px-4 py-3 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:w-auto transition-colors duration-200 btn-hover-effect" data-modal-id="close-modal">
                        <i class="fas fa-times mr-2"></i> Annuler
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
