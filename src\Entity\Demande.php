<?php

namespace App\Entity;

use App\Repository\DemandeRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Context\ExecutionContextInterface;

#[ORM\Entity(repositoryClass: DemandeRepository::class)]
#[ORM\HasLifecycleCallbacks]
class Demande
{
    const URGENCE_BASSE = 'basse';
    const URGENCE_MOYENNE = 'moyenne';
    const URGENCE_HAUTE = 'haute';

    const NATURE_ECHANTILLONS = 'echantillons';
    const NATURE_OUTILLAGE = 'outillage';
    const NATURE_PIECES = 'pieces';

    const STATUT_EN_ATTENTE = 'en_attente';
    const STATUT_VALIDEE = 'validee';
    const STATUT_REFUSEE = 'refusee';
    const STATUT_TRAITEE = 'traitee';
    const STATUT_CLOTUREE = 'cloturee';

    const POTENTIEL_ONE_SHOT = 'one_shot';
    const POTENTIEL_RECURRENT = 'recurrent';

    const DESTINATAIRE_USINAGE = 'usinage';
    const DESTINATAIRE_MOULAGE = 'moulage';
    const DESTINATAIRE_ASSEMBLAGE = 'assemblage';
    const DESTINATAIRE_LOGISTIQUE = 'logistique';

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $dateCreation = null;

    #[ORM\Column(type: Types::DATE_MUTABLE)]
    #[Assert\NotBlank(message: "La date souhaitée de réception est obligatoire")]
    #[Assert\GreaterThanOrEqual(value: "today", message: "La date souhaitée doit être supérieure ou égale à aujourd'hui")]
    private ?\DateTimeInterface $dateSouhaitee = null;

    #[ORM\ManyToOne(targetEntity: User::class, inversedBy: 'demandesAssignees')]
    #[ORM\JoinColumn(nullable: true)]
    private ?User $technicien = null;

    #[ORM\Column(length: 20)]
    #[Assert\NotBlank(message: "L'urgence est obligatoire")]
    #[Assert\Choice(choices: [self::URGENCE_BASSE, self::URGENCE_MOYENNE, self::URGENCE_HAUTE], message: "L'urgence sélectionnée n'est pas valide")]
    private ?string $urgence = null;

    #[ORM\Column(length: 20)]
    #[Assert\NotBlank(message: "La nature de la demande est obligatoire")]
    #[Assert\Choice(choices: [self::NATURE_ECHANTILLONS, self::NATURE_OUTILLAGE, self::NATURE_PIECES], message: "La nature sélectionnée n'est pas valide")]
    private ?string $nature = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $destinataire = null;

    #[ORM\ManyToOne(targetEntity: User::class)]
    #[ORM\JoinColumn(nullable: true)]
    private ?User $destinataireUser = null;

    #[ORM\Column(length: 50, nullable: true)]
    private ?string $compte = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $commentaireDemandeur = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $commentaireGestionnaire = null;

    #[ORM\Column(length: 20)]
    private ?string $statut = self::STATUT_EN_ATTENTE;

    #[ORM\ManyToOne(inversedBy: 'demandes')]
    #[ORM\JoinColumn(nullable: false)]
    private ?User $demandeur = null;

    #[ORM\OneToMany(mappedBy: 'demande', targetEntity: DemandeItem::class, orphanRemoval: true, cascade: ['persist', 'remove'])]
    #[Assert\Valid]
    #[Assert\Count(min: 1, minMessage: "Vous devez ajouter au moins un item à votre demande")]
    private Collection $items;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $wbs = null;

    #[ORM\Column(type: Types::DATE_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $dateGestionnaire = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $lienPlan = null;

    #[ORM\Column(length: 50, nullable: true)]
    private ?string $numeroOF = null;

    #[ORM\Column(length: 20, nullable: true)]
    #[Assert\Choice(choices: [self::POTENTIEL_ONE_SHOT, self::POTENTIEL_RECURRENT], message: "Le potentiel sélectionné n'est pas valide")]
    private ?string $potentiel = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $nomClient = null;

    #[ORM\ManyToOne(targetEntity: User::class)]
    #[ORM\JoinColumn(nullable: true)]
    private ?User $gestionnairePreferentiel = null;

    #[ORM\Column(nullable: true)]
    private ?bool $aSortirEnAchat = false;

    public function __construct()
    {
        $this->items = new ArrayCollection();
        $this->statut = self::STATUT_EN_ATTENTE;
    }

    #[ORM\PrePersist]
    public function setDateCreationValue(): void
    {
        $this->dateCreation = new \DateTime();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getDateCreation(): ?\DateTimeInterface
    {
        return $this->dateCreation;
    }

    public function setDateCreation(\DateTimeInterface $dateCreation): static
    {
        $this->dateCreation = $dateCreation;

        return $this;
    }

    public function getDateSouhaitee(): ?\DateTimeInterface
    {
        return $this->dateSouhaitee;
    }

    public function setDateSouhaitee(\DateTimeInterface $dateSouhaitee): static
    {
        $this->dateSouhaitee = $dateSouhaitee;

        return $this;
    }

    public function getTechnicien(): ?User
    {
        return $this->technicien;
    }

    public function setTechnicien(?User $technicien): static
    {
        $this->technicien = $technicien;

        return $this;
    }

    /**
     * Alias pour getTechnicien() pour compatibilité
     */
    public function getPersonneConcernee(): ?User
    {
        return $this->technicien;
    }

    /**
     * Alias pour setTechnicien() pour compatibilité
     */
    public function setPersonneConcernee(?User $technicien): static
    {
        return $this->setTechnicien($technicien);
    }

    public function getUrgence(): ?string
    {
        return $this->urgence;
    }

    public function setUrgence(string $urgence): static
    {
        $this->urgence = $urgence;

        return $this;
    }

    public function getNature(): ?string
    {
        return $this->nature;
    }

    public function setNature(string $nature): static
    {
        $this->nature = $nature;

        return $this;
    }

    public function getDestinataire(): ?string
    {
        return $this->destinataire;
    }

    public function setDestinataire(?string $destinataire): static
    {
        $this->destinataire = $destinataire;

        return $this;
    }

    public function getDestinataireUser(): ?User
    {
        return $this->destinataireUser;
    }

    public function setDestinataireUser(?User $destinataireUser): static
    {
        $this->destinataireUser = $destinataireUser;

        return $this;
    }

    public function getDestinataireComplet(): string
    {
        if ($this->destinataireUser) {
            return $this->destinataireUser->getNomComplet();
        }

        return $this->destinataire ?? 'Non défini';
    }

    public function getCompte(): ?string
    {
        return $this->compte;
    }

    public function setCompte(string $compte): static
    {
        $this->compte = $compte;

        return $this;
    }

    public function getCommentaireDemandeur(): ?string
    {
        return $this->commentaireDemandeur;
    }

    public function setCommentaireDemandeur(?string $commentaireDemandeur): static
    {
        $this->commentaireDemandeur = $commentaireDemandeur;

        return $this;
    }

    public function getCommentaireGestionnaire(): ?string
    {
        return $this->commentaireGestionnaire;
    }

    public function setCommentaireGestionnaire(?string $commentaireGestionnaire): static
    {
        $this->commentaireGestionnaire = $commentaireGestionnaire;

        return $this;
    }

    public function getStatut(): ?string
    {
        return $this->statut;
    }

    public function setStatut(string $statut): static
    {
        $this->statut = $statut;

        return $this;
    }

    public function getDemandeur(): ?User
    {
        return $this->demandeur;
    }

    public function setDemandeur(?User $demandeur): static
    {
        $this->demandeur = $demandeur;

        return $this;
    }

    /**
     * @return Collection<int, DemandeItem>
     */
    public function getItems(): Collection
    {
        return $this->items;
    }

    public function addItem(DemandeItem $item): static
    {
        if (!$this->items->contains($item)) {
            $this->items->add($item);
            $item->setDemande($this);
        }

        return $this;
    }

    public function removeItem(DemandeItem $item): static
    {
        if ($this->items->removeElement($item)) {
            // set the owning side to null (unless already changed)
            if ($item->getDemande() === $this) {
                $item->setDemande(null);
            }
        }

        return $this;
    }

    public function getUrgenceLabel(): string
    {
        return match($this->urgence) {
            self::URGENCE_BASSE => 'Basse',
            self::URGENCE_MOYENNE => 'Moyenne',
            self::URGENCE_HAUTE => 'Haute',
            default => 'Inconnue'
        };
    }

    public function getNatureLabel(): string
    {
        return match($this->nature) {
            self::NATURE_ECHANTILLONS => 'Échantillons',
            self::NATURE_OUTILLAGE => 'Outillage',
            self::NATURE_PIECES => 'Pièces',
            default => 'Inconnue'
        };
    }

    public function getStatutLabel(): string
    {
        return match($this->statut) {
            self::STATUT_EN_ATTENTE => 'En attente',
            self::STATUT_VALIDEE => 'Validée',
            self::STATUT_REFUSEE => 'Refusée',
            self::STATUT_TRAITEE => 'Traitée',
            self::STATUT_CLOTUREE => 'Clôturée',
            default => 'Inconnu'
        };
    }

    public function getWbs(): ?string
    {
        return $this->wbs;
    }

    public function setWbs(?string $wbs): static
    {
        $this->wbs = $wbs;

        return $this;
    }

    public function getDateGestionnaire(): ?\DateTimeInterface
    {
        return $this->dateGestionnaire;
    }

    public function setDateGestionnaire(?\DateTimeInterface $dateGestionnaire): static
    {
        $this->dateGestionnaire = $dateGestionnaire;

        return $this;
    }

    public function getLienPlan(): ?string
    {
        return $this->lienPlan;
    }

    public function setLienPlan(?string $lienPlan): static
    {
        $this->lienPlan = $lienPlan;

        return $this;
    }

    public function getNumeroOF(): ?string
    {
        return $this->numeroOF;
    }

    public function setNumeroOF(?string $numeroOF): static
    {
        $this->numeroOF = $numeroOF;

        return $this;
    }

    public function getPotentiel(): ?string
    {
        return $this->potentiel;
    }

    public function setPotentiel(?string $potentiel): static
    {
        $this->potentiel = $potentiel;

        return $this;
    }

    public function getPotentielLabel(): ?string
    {
        return match($this->potentiel) {
            self::POTENTIEL_ONE_SHOT => 'One Shot',
            self::POTENTIEL_RECURRENT => 'Récurrent',
            default => null
        };
    }

    public function getNomClient(): ?string
    {
        return $this->nomClient;
    }

    public function setNomClient(?string $nomClient): static
    {
        $this->nomClient = $nomClient;

        return $this;
    }

    public function getGestionnairePreferentiel(): ?User
    {
        return $this->gestionnairePreferentiel;
    }

    public function setGestionnairePreferentiel(?User $gestionnairePreferentiel): static
    {
        $this->gestionnairePreferentiel = $gestionnairePreferentiel;

        return $this;
    }

    public function isASortirEnAchat(): ?bool
    {
        return $this->aSortirEnAchat;
    }

    public function setASortirEnAchat(?bool $aSortirEnAchat): static
    {
        $this->aSortirEnAchat = $aSortirEnAchat;

        return $this;
    }

    #[Assert\Callback]
    public function validateCompteOrWbs(ExecutionContextInterface $context): void
    {
        if (empty($this->compte) && empty($this->wbs)) {
            $context->buildViolation('Au moins l\'un des champs "Centre de coût" ou "WBS" doit être renseigné.')
                ->atPath('compte')
                ->addViolation();
        }
    }
}
