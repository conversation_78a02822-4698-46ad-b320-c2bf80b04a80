{% extends 'base.html.twig' %}

{% block title %}Nouvelle demande{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        /* Styles pour les sections du formulaire */
        .form-section {
            border-radius: 0.5rem;
            background-color: white;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .form-section:hover {
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .form-section-header {
            background-color: #F9FAFB;
            border-bottom: 1px solid #E5E7EB;
            padding: 1rem 1.5rem;
        }

        .form-section-body {
            padding: 1.5rem;
        }

        /* Styles pour les champs de formulaire */
        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 0.5rem 0.75rem;
            border: 1px solid #D1D5DB;
            border-radius: 0.375rem;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
            transition: all 0.2s ease;
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            border-color: #3B82F6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
            outline: none;
        }

        /* Styles pour les items */
        .item-row {
            transition: all 0.2s ease;
            background-color: #F9FAFB;
        }

        .item-row:hover {
            background-color: #F3F4F6;
            transform: translateY(-1px);
        }

        /* Styles pour les boutons */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.5rem 1rem;
            font-weight: 500;
            border-radius: 0.375rem;
            transition: all 0.2s ease;
        }

        .btn:hover {
            transform: translateY(-1px);
        }

        .btn-primary {
            background-color: #3B82F6;
            color: white;
            border: 1px solid transparent;
        }

        .btn-primary:hover {
            background-color: #2563EB;
            box-shadow: 0 4px 6px rgba(59, 130, 246, 0.2);
        }

        .btn-secondary {
            background-color: white;
            color: #4B5563;
            border: 1px solid #D1D5DB;
        }

        .btn-secondary:hover {
            background-color: #F9FAFB;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

        .btn-success {
            background-color: #10B981;
            color: white;
            border: 1px solid transparent;
        }

        .btn-success:hover {
            background-color: #059669;
            box-shadow: 0 4px 6px rgba(16, 185, 129, 0.2);
        }

        .btn-danger {
            background-color: #EF4444;
            color: white;
            border: 1px solid transparent;
        }

        .btn-danger:hover {
            background-color: #DC2626;
            box-shadow: 0 4px 6px rgba(239, 68, 68, 0.2);
        }
    </style>
{% endblock %}

{% block body %}
<div class="max-w-7xl mx-auto space-y-6 py-6">
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Nouvelle demande</h1>
            <p class="mt-1 text-sm text-gray-600">Créez une nouvelle demande en remplissant le formulaire ci-dessous</p>
        </div>
        <a href="{{ path('app_demande_index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left mr-2"></i>
            Retour à la liste
        </a>
    </div>

    <div class="form-section">
        <div class="form-section-body">
            {{ include('demande/_form.html.twig') }}
        </div>
    </div>
</div>
{% endblock %}


