<?php

namespace App\Repository;

use App\Entity\User;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Bridge\Doctrine\Security\User\UserLoaderInterface;
use App\Service\LdapService;
use App\Entity\Service;

/**
 * @extends ServiceEntityRepository<User>
 */
class UserRepository extends ServiceEntityRepository implements UserLoaderInterface
{
    private $ldapService;

    public function __construct(ManagerRegistry $registry, LdapService $ldapService)
    {
        parent::__construct($registry, User::class);
        $this->ldapService = $ldapService;
    }

    public function loadUserByIdentifier(string $usernameOrEmail): ?User
    {
        $entityManager = $this->getEntityManager();
        $username = explode("\\", $_SERVER['REMOTE_USER'])[1];
        if($username == "eerdmann") {
            $username ='mmedard';
        }
        // if($username == "acrapis") {
        //     // $username ='acrapis';
        //     // $username ='jfcribier';
        //     $username ='tlelong';

        // }
        if($username == "SCMXIBO") {
                // $username ='acrapis';
                // $username ='jfcribier';
            $username ='eerdmann';
        }
        if($username == "srochdi") {
            // $username ='lmartineau';
            // $username ='jfcribier';
            // $username ='jhochart';
            //    $username ='acrapis';
        }
        // $allusers = $this->ldapService->getAllUsersInfo();
        // dd($allusers);
        // foreach ($allusers as $ldapUserInfo) {

        //     $secteurRepository = $entityManager->getRepository(Service::class);
        //     $secteur = $secteurRepository->findOneBy(['name' => $ldapUserInfo['department']]);

        //     if (!$secteur && isset($ldapUserInfo['department'])) {
        //         $secteur = new Service();
        //         $secteur->setName($ldapUserInfo['department']);
        //         $entityManager->persist($secteur);
        //         $entityManager->flush();
        //     }

        //     $existingUser = $this->findOneBy(['username' => $ldapUserInfo['username']]);
        //     $nomComplet = preg_match('/CN=([^,]+)/', $ldapUserInfo['distinguishedName'], $matches);
        //     if ($nomComplet) {
        //         $test = explode(' ', $matches[1]);
        //         if (count($test) < 2) {
        //             continue;
        //         }else{

        //             list($nom, $prenom) = explode(' ', $matches[1]);
        //         }
        //     }
        //     $department = $this->normalizeDepartment($ldapUserInfo['department'] ?? 'DEFAULT');
        //     $roles = $ldapUserInfo['isManager']
        //         ? ['ROLE_MANAGER_' . $department]
        //         : ['ROLE_USER_' . $department];
        //     $isInVpn = $this->ldapService->isUserInVpn($ldapUserInfo['username'], 'VPN_SSL', 'OU=FRSCM_Groupes,DC=scmlemans,DC=com');
        //     if ($existingUser) {
        //         // Mettre à jour l'utilisateur existant
        //         $existingUser->setEmail($ldapUserInfo['email'] ?? $existingUser->getEmail());
        //         $existingUser->setNom($nom ?? $existingUser->getNom());
        //         $existingUser->setPrenom($prenom ?? $existingUser->getPrenom());
        //         $existingUser->setRoles($roles);
        //         $existingUser->setSecteur($ldapUserInfo['department'] ?? $existingUser->getSecteur());
        //         $existingUser->setManager($ldapUserInfo['manager']['distinguishedName'] ?? $existingUser->getManager());
        //         $existingUser->setIsManager($ldapUserInfo['isManager'] ?? $existingUser->getIsManager());
        //         $existingUser->setTitre($ldapUserInfo['title'] ?? $existingUser->getTitre());
        //         $existingUser->setVpn($isInVpn);
        //         $existingUser->setMobile($ldapUserInfo['mobile'] ?? null);

        //         $entityManager->flush();

        //     } elseif ($ldapUserInfo['email'] != "<EMAIL>") {
        //         // Créer un nouvel utilisateur
        //         $newUser = new User();
        //         $newUser->setUsername($ldapUserInfo['username']);
        //         $newUser->setEmail($ldapUserInfo['email'] ?? null);
        //         $newUser->setNom($nom ?? null);
        //         $newUser->setPrenom($prenom ?? null);
        //         $newUser->setRoles($roles);
        //         $newUser->setSecteur($ldapUserInfo['department'] ?? 'null');
        //         $newUser->setManager($ldapUserInfo['manager']['distinguishedName'] ?? null);
        //         $newUser->setIsManager($ldapUserInfo['isManager'] ?? null);
        //         $newUser->setTitre($ldapUserInfo['title'] ?? null);
        //         $newUser->setVpn($isInVpn);
        //         $newUser->setMobile($ldapUserInfo['mobile'] ?? null);
        //         $entityManager->persist($newUser);
        //         $entityManager->flush();

        //     }
        // }

        $ldapUserInfo = $this->ldapService->getUserInfoByUsernameInscription($username);
        $isInVpn = true;

        $secteurRepository = $entityManager->getRepository(Service::class);
        $secteur = $secteurRepository->findOneBy(['name' => $ldapUserInfo['department']]);

        if (!$secteur && isset($ldapUserInfo['department'])) {
            $secteur = new Service();
            $secteur->setName($ldapUserInfo['department']);
            $entityManager->persist($secteur);
            $entityManager->flush();
        }

        $existingUser = $this->findOneBy(['username' => $ldapUserInfo['username']]);
        $nomComplet = preg_match('/CN=([^,]+)/', $ldapUserInfo['distinguishedName'], $matches);
        if ($nomComplet) {
            list($nom, $prenom) = explode(' ', $matches[1], 2);
        }
        $department = $this->normalizeDepartment($ldapUserInfo['department'] ?? 'DEFAULT');
        $roles = $ldapUserInfo['isManager']
            ? ['ROLE_MANAGER_' . $department]
            : ['ROLE_USER_' . $department];

        if ($existingUser) {
            // Mettre à jour l'utilisateur existant

            $existingUser->setEmail($ldapUserInfo['email'] ?? $existingUser->getEmail());
            $existingUser->setNom($nom ?? $existingUser->getNom());
            $existingUser->setPrenom($prenom ?? $existingUser->getPrenom());
            $existingUser->setRoles($roles);
            $existingUser->setSecteur($ldapUserInfo['department'] ?? $existingUser->getSecteur());
            $existingUser->setManager($ldapUserInfo['manager']['distinguishedName'] ?? $existingUser->getManager());
            $existingUser->setIsManager($ldapUserInfo['isManager'] ?? $existingUser->getIsManager());
            $existingUser->setTitre($ldapUserInfo['title'] ?? $existingUser->getTitre());
            $existingUser->setVpn($isInVpn);
            $existingUser->setMobile($ldapUserInfo['mobile'] ?? $existingUser->getMobile());
            $existingUser->setTelephoneNumber($ldapUserInfo['telephoneNumber'] ?? $existingUser->getTelephoneNumber());
            $entityManager->flush();
            return $existingUser;
        } else {
            // Créer un nouvel utilisateur
            $newUser = new User();
            $newUser->setUsername($ldapUserInfo['username']);
            $newUser->setEmail($ldapUserInfo['email'] ?? null);
            $newUser->setNom($nom ?? null);
            $newUser->setPrenom($prenom ?? null);
            $newUser->setRoles($roles);
            $newUser->setSecteur($ldapUserInfo['department'] ?? 'null');
            $newUser->setManager($ldapUserInfo['manager']['distinguishedName'] ?? null);
            $newUser->setIsManager($ldapUserInfo['isManager'] ?? null);
            $newUser->setTitre($ldapUserInfo['title'] ?? null);
            $newUser->setVpn($isInVpn);
            $newUser->setMobile($ldapUserInfo['mobile'] ?? null);
            $newUser->setTelephoneNumber($ldapUserInfo['telephoneNumber'] ?? null);
            $entityManager->persist($newUser);
            $entityManager->flush();
            return $newUser;
        }
    }

    private function normalizeDepartment(?string $department): string
    {
        if (empty($department)) {
            return 'DEFAULT';
        }
        // Remplace les espaces ou caractères spéciaux par des underscores
        $normalized = preg_replace('/[^A-Z0-9]/', '_', strtoupper($department));

        return $normalized;
    }

    public function explodeRole(array $roles): array
    {
        $newRoles = [];
        foreach ($roles as $role) {
            $newRoles[] = explode('_', $role);
        }
        return $newRoles;
    }

    /**
     * Trouve les utilisateurs dont le manager contient le nom et prénom spécifiés
     */
    public function findUsersWithManager(string $nom, string $prenom): array
    {
        $qb = $this->createQueryBuilder('u');
        $qb->where('u.manager LIKE :manager')
           ->setParameter('manager', '%' . $nom . ' ' . $prenom . '%')
           ->orderBy('u.nom', 'ASC');

        return $qb->getQuery()->getResult();
    }

    /**
     * Récupère tous les utilisateurs
     */
    public function findAllWithForfait(): array
    {
        return $this->createQueryBuilder('u')
            ->orderBy('u.nom', 'ASC')
            ->addOrderBy('u.prenom', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Récupère tous les utilisateurs qui ont un numéro de téléphone mobile
     */
    public function findAllWithMobile(): array
    {
        return $this->createQueryBuilder('u')
            ->where('u.mobile IS NOT NULL')
            ->orderBy('u.nom', 'ASC')
            ->addOrderBy('u.prenom', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Récupère tous les utilisateurs qui ont un numéro de téléphone (mobile ou fixe)
     */
    public function findAllWithPhoneNumbers(): array
    {
        return $this->createQueryBuilder('u')
            ->where('u.mobile IS NOT NULL OR u.telephoneNumber IS NOT NULL')
            ->orderBy('u.nom', 'ASC')
            ->addOrderBy('u.prenom', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Recherche des utilisateurs par nom, prénom ou numéro de téléphone
     */
    public function searchByNameOrPhoneNumber(string $query): array
    {
        return $this->createQueryBuilder('u')
            ->where('u.nom LIKE :query')
            ->orWhere('u.prenom LIKE :query')
            ->orWhere('u.mobile LIKE :query')
            ->orWhere('u.telephoneNumber LIKE :query')
            ->orWhere('u.email LIKE :query')
            ->andWhere('u.mobile IS NOT NULL OR u.telephoneNumber IS NOT NULL')
            ->setParameter('query', '%' . $query . '%')
            ->orderBy('u.nom', 'ASC')
            ->addOrderBy('u.prenom', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Recherche des utilisateurs par nom, prénom ou email
     */
    public function searchByNameOrEmail(string $query): array
    {
        return $this->createQueryBuilder('u')
            ->where('u.nom LIKE :query')
            ->orWhere('u.prenom LIKE :query')
            ->orWhere('u.email LIKE :query')
            ->setParameter('query', '%' . $query . '%')
            ->orderBy('u.nom', 'ASC')
            ->addOrderBy('u.prenom', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Récupère tous les utilisateurs ayant des rôles avancés
     */
    public function findByAdvancedRoles(): array
    {
        $advancedRoles = [
            'ROLE_USER_APPROVISIONNEMENT',
            'ROLE_MANAGER_GAP',
            'ROLE_MANAGER_USINAGE',
            'ROLE_MANAGER_DIRECTION',
            'ROLE_MANAGER_SUPPLY_CHAIN',
            'ROLE_ADMIN'
        ];

        $qb = $this->createQueryBuilder('u');

        // Construire la requête pour trouver les utilisateurs avec au moins un des rôles avancés
        // OU l'utilisateur eerdmann spécifiquement
        $orExpressions = $qb->expr()->orX();

        // Ajouter la condition pour les rôles
        foreach ($advancedRoles as $index => $role) {
            $orExpressions->add($qb->expr()->like('u.roles', ':role' . $index));
            $qb->setParameter('role' . $index, '%' . $role . '%');
        }

        // Ajouter la condition pour eerdmann
        $orExpressions->add($qb->expr()->eq('u.username', ':eerdmann'));
        $qb->setParameter('eerdmann', 'eerdmann');

        // Exclure uniquement l'utilisateur fkleindienst
        $qb->andWhere('u.username != :excluded_user')
           ->setParameter('excluded_user', 'fkleindienst');

        $qb->andWhere($orExpressions)
           ->orderBy('u.nom', 'ASC')
           ->addOrderBy('u.prenom', 'ASC');

        return $qb->getQuery()->getResult();
    }

    /**
     * Met à jour un utilisateur avec les informations LDAP
     */
    public function updateUserFromLdap(string $username, $entityManager): ?User
    {
        // Récupérer les informations LDAP de l'utilisateur
        $ldapUserInfo = $this->ldapService->getUserInfoByUsernameInscription($username);
        if (!$ldapUserInfo) {
            return null;
        }

        // Vérifier si l'utilisateur est dans le groupe VPN
        $isInVpn = $this->ldapService->isUserInVpn($username, 'VPN_SSL', 'OU=FRSCM_Groupes,DC=scmlemans,DC=com');

        // Vérifier si le service existe, sinon le créer
        $secteurRepository = $entityManager->getRepository(Service::class);
        $secteur = $secteurRepository->findOneBy(['name' => $ldapUserInfo['department']]);

        if (!$secteur && isset($ldapUserInfo['department'])) {
            $secteur = new Service();
            $secteur->setName($ldapUserInfo['department']);
            $entityManager->persist($secteur);
            $entityManager->flush();
        }

        // Extraire le nom et prénom du DN
        $nom = null;
        $prenom = null;
        $nomComplet = preg_match('/CN=([^,]+)/', $ldapUserInfo['distinguishedName'], $matches);
        if ($nomComplet) {
            list($nom, $prenom) = explode(' ', $matches[1], 2);
        }

        // Déterminer les rôles
        $department = $this->normalizeDepartment($ldapUserInfo['department'] ?? 'DEFAULT');
        $roles = $ldapUserInfo['isManager']
            ? ['ROLE_MANAGER_' . $department]
            : ['ROLE_USER_' . $department];

        // Trouver l'utilisateur existant
        $existingUser = $this->findOneBy(['username' => $username]);

        if ($existingUser) {
            // Mettre à jour l'utilisateur existant
            $existingUser->setEmail($ldapUserInfo['email'] ?? $existingUser->getEmail());
            $existingUser->setNom($nom ?? $existingUser->getNom());
            $existingUser->setPrenom($prenom ?? $existingUser->getPrenom());
            $existingUser->setRoles($roles);
            $existingUser->setSecteur($ldapUserInfo['department'] ?? $existingUser->getSecteur());
            $existingUser->setManager($ldapUserInfo['manager']['distinguishedName'] ?? $existingUser->getManager());
            $existingUser->setIsManager($ldapUserInfo['isManager'] ?? $existingUser->getIsManager());
            $existingUser->setTitre($ldapUserInfo['title'] ?? $existingUser->getTitre());
            $existingUser->setVpn($isInVpn);
            $existingUser->setMobile($ldapUserInfo['mobile'] ?? $existingUser->getMobile());
            $existingUser->setTelephoneNumber($ldapUserInfo['telephoneNumber'] ?? $existingUser->getTelephoneNumber());

            $entityManager->flush();
            return $existingUser;
        }

        return null;
    }

    /**
     * Récupère tous les secteurs distincts des utilisateurs
     */
    public function findAllDistinctSecteurs(): array
    {
        $conn = $this->getEntityManager()->getConnection();
        $sql = "SELECT DISTINCT secteur FROM user WHERE secteur IS NOT NULL AND secteur != 'null' ORDER BY secteur";
        $stmt = $conn->prepare($sql);
        $result = $stmt->executeQuery();

        $secteurs = [];
        foreach ($result->fetchAllAssociative() as $row) {
            $secteurs[] = $row['secteur'];
        }

        return $secteurs;
    }
}
